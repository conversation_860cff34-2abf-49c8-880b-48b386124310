<script lang="jsx">
import { toRaw } from 'vue'
import Canvas from '@antv/f-vue'
import { Chart, Interval, Axis } from '@antv/f2'

const data = [
  {
    time: '2017-10-24',
    // 格式为：[open, close, lowest, highest]
    value: [20, 34, 10, 38],
  },
  {
    time: '2017-10-25',
    value: [40, 35, 30, 50],
  },
  {
    time: '2017-10-26',
    value: [31, 38, 33, 44],
  },
  {
    time: '2017-10-27',
    value: [38, 15, 5, 42],
  },
]
export default {
  name: 'App',
  data() {
    return {
      year: '2021',
      chartData: data,
    }
  },
  mounted() {
    // setTimeout(() => {
    //   this.year = '2022'
    //   this.chartData = data2
    // }, 1000)
  },
  render() {
    const { year, chartData } = this
    return (
      <div class="container">
        <Canvas context={context} pixelRatio={window.devicePixelRatio}>
          <Chart data={data}>
            <Axis field="time" type="timeCat" />
            <Axis field="value" formatter={(v) => Number(v).toFixed(0)} />
            <Candlestick x="time" y="value" />
            <Tooltip
              showCrosshairs={true}
              yPositionType="coord"
              crosshairsType="xy"
              showXTip
              showYTip
            />
          </Chart>
        </Canvas>
      </div>
    )
  },
}
</script>

<style>
.container {
  width: 100%;
  height: 300px;
}
</style>
