import React, { useEffect, useRef } from 'react'
import Canvas from '@antv/f-vue'
import { Chart, Candlestick, Axis, Tooltip } from '@antv/f2'

interface DataPoint {
  time: string
  value: [number, number, number, number] // [open, close, lowest, highest]
}

const data: DataPoint[] = [
  {
    time: '2017-10-24',
    // 格式为：[open, close, lowest, highest]
    value: [20, 34, 10, 38],
  },
  {
    time: '2017-10-25',
    value: [40, 35, 30, 50],
  },
  {
    time: '2017-10-26',
    value: [31, 38, 33, 44],
  },
  {
    time: '2017-10-27',
    value: [38, 15, 5, 42],
  },
]

const ChartView: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // 组件挂载后的逻辑
  }, [])

  const getContext = (): CanvasRenderingContext2D | null => {
    const container = containerRef.current
    if (container) {
      const canvas = container.querySelector('canvas')
      return canvas?.getContext('2d') || null
    }
    return null
  }

  return (
    <div className="container" ref={containerRef}>
      <Canvas 
        context={getContext()} 
        pixelRatio={window.devicePixelRatio}
      >
        <Chart data={data}>
          <Axis field="time" type="timeCat" />
          <Axis field="value" formatter={(v: any) => Number(v).toFixed(0)} />
          <Candlestick x="time" y="value" />
          <Tooltip
            showCrosshairs={true}
            yPositionType="coord"
            crosshairsType="xy"
            showXTip
            showYTip
          />
        </Chart>
      </Canvas>
    </div>
  )
}

export default ChartView
