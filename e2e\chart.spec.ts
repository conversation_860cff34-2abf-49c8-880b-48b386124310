import { test, expect } from '@playwright/test'

test.describe('F2 Chart Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/chart')
  })

  test('should display chart page title', async ({ page }) => {
    await expect(page.locator('h1')).toHaveText('F2 图表示例')
  })

  test('should display all chart sections', async ({ page }) => {
    // 检查柱状图标题
    await expect(page.locator('h2').nth(0)).toHaveText('柱状图')
    
    // 检查折线图标题
    await expect(page.locator('h2').nth(1)).toHaveText('折线图')
    
    // 检查饼图标题
    await expect(page.locator('h2').nth(2)).toHaveText('饼图')
  })

  test('should render chart containers', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForTimeout(1000)
    
    // 检查图表容器是否存在
    const chartContainers = page.locator('.chart')
    await expect(chartContainers).toHaveCount(3)
    
    // 检查每个图表容器是否可见
    for (let i = 0; i < 3; i++) {
      await expect(chartContainers.nth(i)).toBeVisible()
    }
  })

  test('should have proper navigation', async ({ page }) => {
    // 检查导航链接是否存在
    await page.goto('/')
    
    // 点击Charts导航链接
    await page.click('text=Charts')
    
    // 验证页面跳转到图表页面
    await expect(page).toHaveURL('/chart')
    await expect(page.locator('h1')).toHaveText('F2 图表示例')
  })

  test('should have responsive layout', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.chart-container')).toBeVisible()
    
    // 测试移动视图
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('.chart-container')).toBeVisible()
    
    // 检查图表容器在移动设备上仍然可见
    const chartContainers = page.locator('.chart')
    await expect(chartContainers).toHaveCount(3)
  })

  test('should load charts without errors', async ({ page }) => {
    // 监听控制台错误
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // 等待图表加载
    await page.waitForTimeout(2000)
    
    // 检查是否有JavaScript错误
    expect(consoleErrors.length).toBe(0)
    
    // 检查图表容器内是否有内容（SVG或Canvas元素）
    const chartElements = await page.locator('.chart canvas, .chart svg').count()
    expect(chartElements).toBeGreaterThan(0)
  })

  test('should have proper styling', async ({ page }) => {
    // 检查主容器样式
    const container = page.locator('.chart-container')
    await expect(container).toHaveCSS('max-width', '800px')
    
    // 检查图表区域样式
    const chartSections = page.locator('.chart-section')
    await expect(chartSections.first()).toBeVisible()
    
    // 检查图表容器边框
    const charts = page.locator('.chart')
    await expect(charts.first()).toHaveCSS('border-width', '1px')
  })
})
